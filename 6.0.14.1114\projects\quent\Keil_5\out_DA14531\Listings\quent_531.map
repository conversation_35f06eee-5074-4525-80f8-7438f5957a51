Component: ARM Compiler 5.06 update 1 (build 61) Tool: armlink [4d35a8]

==============================================================================

Section Cross References

    system_da14531.o(.text) refers to system_da14531.o(.data) for .data
    system_da14531.o(.text) refers to otp_cs.o(otp_cs_booter) for booter_val
    startup_da14531.o(RESET) refers to startup_da14531.o(STACK) for __initial_sp
    startup_da14531.o(RESET) refers to startup_da14531.o(.text) for Reset_Handler
    startup_da14531.o(RESET) refers to rwble.o(.text) for BLE_WAKEUP_LP_Handler
    startup_da14531.o(RESET) refers to user_periph_setup.o(.text) for UART_Handler
    startup_da14531.o(RESET) refers to uart.o(.text) for UART2_Handler
    startup_da14531.o(RESET) refers to adc_531.o(.text) for ADC_Handler
    startup_da14531.o(RESET) refers to gpio.o(.text) for GPIO0_Handler
    startup_da14531.o(.text) refers to system_da14531.o(.text) for SystemInit
    startup_da14531.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    startup_da14531.o(.text) refers to nmi_handler.o(.text) for NMI_HandlerC
    startup_da14531.o(.text) refers to hardfault_handler.o(.text) for HardFault_HandlerC
    nvds.o(.constdata) refers to nvds.o(.constdata) for nvds_data_storage
    nvds.o(.constdata) refers to arch_system.o(retention_mem_area0) for dev_bdaddr
    arch_main.o(.text) refers to arch_system.o(.text) for system_init
    arch_main.o(.text) refers to serialinterface.o(.text) for EnableRFSwitch
    arch_main.o(.text) refers to rwip.o(.text) for rwip_sleep
    arch_main.o(.text) refers to arch_sleep.o(.text) for arch_get_sleep_mode
    arch_main.o(.text) refers to user_periph_setup.o(.text) for periph_init
    arch_main.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_init
    arch_main.o(.text) refers (Weak) to hw_otpc_531.o(.text) for hw_otpc_clear_dcdc_reserved
    arch_main.o(.text) refers to syscntl.o(.text) for syscntl_dcdc_set_level
    arch_main.o(.text) refers to rwble.o(retention_mem_area0) for arch_rwble_last_event
    arch_main.o(.text) refers to otp_cs.o(otp_cs_booter) for booter_val
    arch_main.o(.text) refers to arch_main.o(retention_mem_area0) for retention_mem_area0
    arch_main.o(.constdata) refers to user_peripheral.o(.text) for user_app_init
    jump_table.o(.constdata) refers to ble_arp.o(.text) for rf_init_func
    jump_table.o(.constdata) refers to uart.o(.text) for UART_Handler_SDK_func
    jump_table.o(.constdata) refers to jump_table.o(.text) for platform_reset_func
    jump_table.o(.constdata) refers to rwble.o(.text) for lld_sleep_compensate_func
    jump_table.o(.constdata) refers to arch_system.o(.text) for lld_sleep_init_func
    jump_table.o(.constdata) refers to patch.o(.text) for JT_lld_test_mode_rx_func
    jump_table.o(.constdata) refers to prf.o(.text) for prf_init_func
    jump_table.o(.constdata) refers to jump_table.o(heap_mem_area_not_ret) for rwip_heap_non_ret
    jump_table.o(.constdata) refers to jump_table.o(heap_env_area) for rwip_heap_env_ret
    jump_table.o(.constdata) refers to jump_table.o(heap_db_area) for rwip_heap_db_ret
    jump_table.o(.constdata) refers to jump_table.o(heap_msg_area) for rwip_heap_msg_ret
    jump_table.o(.constdata) refers to nvds.o(.constdata) for rom_nvds_cfg
    jump_table.o(.constdata) refers to custs1.o(.constdata) for rom_cust_prf_cfg
    jump_table.o(.constdata) refers to prf.o(.constdata) for rom_prf_cfg
    jump_table.o(.constdata) refers to app_entry_point.o(.constdata) for rom_app_task_cfg
    arch_sleep.o(.text) refers to arch_sleep.o(retention_mem_area0) for retention_mem_area0
    arch_sleep.o(.text) refers to arch_sleep.o(.text) for arch_set_extended_sleep
    arch_sleep.o(.text) refers to arch_sleep.o(retention_mem_area0) for retention_mem_area0
    arch_sleep.o(.text) refers to arch_sleep.o(.text) for arch_get_sleep_mode
    arch_sleep.o(.text) refers to arch_sleep.o(retention_mem_area0) for retention_mem_area0
    arch_sleep.o(.text) refers to arch_sleep.o(retention_mem_area0) for retention_mem_area0
    arch_sleep.o(.text) refers to arch_sleep.o(retention_mem_area0) for retention_mem_area0
    arch_sleep.o(.text) refers to rwble.o(retention_mem_area0) for arch_rwble_last_event
    arch_system.o(.text) refers to arch_system.o(i.__ARM_common_ll_muluu) for __ARM_common_ll_muluu
    arch_system.o(.text) refers to otp_cs.o(.text) for otp_cs_get_xtal_wait_trim
    arch_system.o(.text) refers to adc_531.o(.text) for adc_init
    arch_system.o(.text) refers to ble_arp.o(.text) for rf_recalibration
    arch_system.o(.text) refers to gpio.o(.text) for GPIO_init
    arch_system.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_init
    arch_system.o(.text) refers to otp_hdr.o(.text) for otp_hdr_get_bd_address
    arch_system.o(.text) refers to arch_system.o(retention_mem_area0) for retention_mem_area0
    arch_system.o(.text) refers to arch_system.o(.bss) for .bss
    arch_system.o(.text) refers to arch_system.o(.constdata) for .constdata
    arch_system.o(.text) refers to arch_rom.o(.text) for arch_rom_init
    arch_system.o(.text) refers to user_periph_setup.o(.text) for periph_init
    arch_system.o(.text) refers to trng.o(.text) for init_rand_seed_from_trng
    arch_system.o(.text) refers to arch_sleep.o(.text) for arch_disable_sleep
    arch_system.o(.text) refers to app.o(.text) for app_init
    arch_system.o(.text) refers to comm_task.o(.text) for comm_init
    arch_system.o(.text) refers to user_peripheral.o(.text) for user_app_init
    arch_system.o(.text) refers to arch_system.o(retention_mem_area0) for retention_mem_area0
    arch_system.o(.text) refers to arch_system.o(.data) for .data
    arch_system.o(.text) refers to arch_system.o(retention_mem_area0) for retention_mem_area0
    arch_system.o(.constdata) refers to user_peripheral.o(.text) for user_app_init
    arch_hibernation.o(.text) refers to arch_hibernation.o(.text) for set_ldo_ret_trim
    arch_hibernation.o(.text) refers to otp_cs.o(otp_cs_booter) for booter_val
    arch_hibernation.o(.text) refers to rwip.o(.text) for patched_ble_regs_push
    arch_hibernation.o(.text) refers to arch_hibernation.o(.text) for set_ldo_ret_trim
    arch_hibernation.o(.text) refers to arch_hibernation.o(retention_mem_area0) for retention_mem_area0
    arch_hibernation.o(.text) refers to otp_cs.o(otp_cs_booter) for booter_val
    arch_hibernation.o(.text) refers to arch_system.o(.text) for set_xtal32m_trim_value
    arch_hibernation.o(.text) refers to user_periph_setup.o(.text) for periph_init
    arch_hibernation.o(.text) refers to rwip.o(.text) for patched_ble_regs_pop
    arch_hibernation.o(.text) refers to otp_cs.o(otp_cs_booter) for booter_val
    arch_hibernation.o(.text) refers to arch_hibernation.o(retention_mem_area0) for retention_mem_area0
    arch_hibernation.o(.text) refers to arch_system.o(retention_mem_area0) for last_temp
    arch_rom.o(.text) refers to patch.o(.text) for patch_global_vars_init
    arch_rom.o(.text) refers to trng.o(trng_state) for trng_state_val
    arch_rom.o(.text) refers to arch_rom.o(.constdata) for .constdata
    arch_rom.o(.text) refers to arch_rom.o(retention_mem_area0) for retention_mem_area0
    arch_rom.o(.text) refers to jump_table.o(.constdata) for rom_func_addr_table_var
    arch_rom.o(.text) refers to jump_table.o(.constdata) for rom_cfg_table_var
    otp_cs.o(.text) refers (Weak) to rf_531.o(.text) for rf_pa_pwr_get
    otp_cs.o(.text) refers (Weak) to rf_531.o(.text) for rf_pa_pwr_get
    otp_cs.o(.text) refers to otp_cs.o(retention_mem_area0) for retention_mem_area0
    otp_cs.o(.text) refers to otp_cs.o(otp_cs_booter) for otp_cs_booter
    otp_cs.o(.text) refers to otp_cs.o(.bss) for .bss
    otp_cs.o(.text) refers to otp_cs.o(retention_mem_area0) for retention_mem_area0
    otp_cs.o(.text) refers to otp_cs.o(retention_mem_area0) for retention_mem_area0
    otp_cs.o(.text) refers to otp_cs.o(retention_mem_area0) for retention_mem_area0
    syscntl.o(.text) refers (Weak) to hw_otpc_531.o(.text) for hw_otpc_is_dcdc_reserved
    syscntl.o(.text) refers to syscntl.o(.bss) for .bss
    syscntl.o(.text) refers (Weak) to hw_otpc_531.o(.text) for hw_otpc_is_dcdc_reserved
    syscntl.o(.text) refers to syscntl.o(.text) for dcdc_cfg
    gpio.o(.text) refers to gpio.o(retention_mem_area0) for retention_mem_area0
    gpio.o(.text) refers to gpio.o(.text) for gpioshift16
    hw_otpc_531.o(.text) refers to syscntl.o(.text) for syscntl_dcdc_get_level
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(retention_mem_area0) for retention_mem_area0
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.constdata) for .constdata
    hw_otpc_531.o(.text) refers to syscntl.o(.bss) for syscntl_dcdc_state
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_enter_mode
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_enter_mode
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_enter_mode
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_prog
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_read_verif
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_enter_mode
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_enter_mode
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_enter_mode
    uart.o(.text) refers to ring_buf.o(.text) for RingBuf_get
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to uart.o(.text) for uart_read_byte
    uart.o(.text) refers to uart.o(.text) for uart_read_buffer
    uart.o(.text) refers to uart.o(.text) for __NVIC_DisableIRQ
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to ring_buf.o(.text) for RingBuf_DeInit
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to uart.o(.text) for __NVIC_DisableIRQ
    uart.o(.text) refers to ring_buf.o(.text) for RingBuf_Init
    uart.o(.text) refers to uart.o(.text) for __NVIC_DisableIRQ
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to uart.o(.text) for uart_enable_flow_control
    trng.o(.text) refers to trng.o(trng_state) for trng_state
    trng.o(.text) refers to arch_system.o(.bss) for otp_hdr_timestamp
    trng.o(.text) refers to trng.o(.bss) for .bss
    adc_531.o(.text) refers to otp_cs.o(.text) for otp_cs_get_adc_25_cal
    adc_531.o(.text) refers to adc_531.o(.bss) for .bss
    adc_531.o(.text) refers to adc_531.o(retention_mem_area0) for retention_mem_area0
    adc_531.o(.text) refers to adc_531.o(.text) for __NVIC_DisableIRQ
    adc_531.o(.text) refers to adc_531.o(.text) for adc_input_shift_enable
    adc_531.o(.text) refers to adc_531.o(retention_mem_area0) for retention_mem_area0
    adc_531.o(.text) refers to adc_531.o(.text) for __NVIC_DisableIRQ
    adc_531.o(.text) refers to adc_531.o(retention_mem_area0) for retention_mem_area0
    adc_531.o(.text) refers to adc_531.o(.text) for adc_start
    adc_531.o(.text) refers to adc_531.o(.text) for adc_init
    adc_531.o(.text) refers to adc_531.o(.text) for adc_get_sample
    adc_531.o(.text) refers to adc_531.o(.text) for adc_correction_apply
    adc_531.o(.text) refers to adc_531.o(.bss) for .bss
    adc_531.o(.text) refers to otp_cs.o(.text) for otp_cs_get_adc_offsh_ge
    adc_531.o(.text) refers to otp_cs.o(.text) for otp_cs_get_adc_offsh_offset
    adc_531.o(.text) refers to otp_cs.o(.text) for otp_cs_get_adc_single_ge
    rwble.o(.text) refers to arch_system.o(.text) for lld_sleep_lpcycles_2_us_sel_func
    rwble.o(.text) refers to syscntl.o(.text) for syscntl_use_highest_amba_clocks
    rwble.o(.text) refers to rwip.o(.text) for patched_ble_regs_pop
    rwble.o(.text) refers to rf_531.o(.text) for rf_adplldig_deactivate
    rwble.o(.text) refers to rwble.o(retention_mem_area0) for retention_mem_area0
    rwble.o(.text) refers to rwip.o(retention_mem_area0) for slp_period_retained
    rwble.o(.text) refers to arch_system.o(retention_mem_area0) for clk_freq_trim_reg_value
    rwip.o(.text) refers to arch_sleep.o(.text) for arch_ble_ext_wakeup_get
    rwip.o(.text) refers to arch_system.o(.text) for set_sleep_delay
    rwip.o(.text) refers to syscntl.o(.text) for syscntl_cfg_xtal32m_amp_reg
    rwip.o(.text) refers to rwip.o(retention_mem_area0) for retention_mem_area0
    rwip.o(.text) refers to arch_system.o(retention_mem_area0) for xtal_wait_trim
    rwip.o(.text) refers to arch_system.o(.bss) for twirq_reset_value
    rwip.o(.text) refers to rwip.o(.bss) for .bss
    rwip.o(.text) refers to rwble.o(retention_mem_area0) for ble_finetim_corr
    ble_arp.o(.text) refers (Weak) to rf_531.o(.text) for rf_pa_pwr_get
    ble_arp.o(.text) refers to syscntl.o(.text) for syscntl_cfg_xtal32m_amp_reg
    ble_arp.o(.text) refers to rf_531.o(.text) for rf_power_up
    ble_arp.o(.text) refers to otp_cs.o(.text) for otp_cs_load_pd_rad
    ble_arp.o(.text) refers to ble_arp.o(.constdata) for .constdata
    rf_531.o(.text) refers to rf_531.o(retention_mem_area0) for retention_mem_area0
    rf_531.o(.text) refers to rf_531.o(.data) for .data
    rf_531.o(.text) refers to rf_531.o(.bss) for .bss
    rf_531.o(.text) refers to rf_531.o(.text) for en_adpll_tx
    rf_531.o(.text) refers to rf_531.o(.bss) for .bss
    rf_531.o(.text) refers to rf_531.o(.bss) for .bss
    rf_531.o(.text) refers to rf_531.o(.bss) for .bss
    rf_531.o(.text) refers to rf_531.o(retention_mem_area0) for retention_mem_area0
    rf_531.o(.text) refers to rf_531.o(retention_mem_area0) for retention_mem_area0
    custs1.o(.constdata) refers to user_custs1_def.o(.constdata) for custs1_services
    custs1.o(.constdata) refers to user_custs1_def.o(.constdata) for custs1_services_size
    custs1.o(.constdata) refers to user_custs1_def.o(.constdata) for custs1_att_db
    custs1.o(.constdata) refers to app_customs.o(.text) for custs_get_func_callbacks
    custs1_task.o(.text) refers to app_customs.o(.text) for custs_get_func_callbacks
    custs1_task.o(.constdata) refers to custs1_task.o(.text) for gattc_write_req_ind_handler
    custs1_task.o(.constdata) refers to custs1_task.o(.constdata) for custs1_default_state
    prf.o(.text) refers to prf.o(retention_mem_area0) for retention_mem_area0
    prf.o(.text) refers to custs1_task.o(.constdata) for custs1_default_handler
    prf.o(.constdata) refers to prf.o(.constdata) for prf_if
    prf.o(.constdata) refers to prf.o(retention_mem_area0) for prf_env
    app_default_handlers.o(.text) refers to app.o(.text) for app_easy_gap_undirected_advertise_start
    app_default_handlers.o(.text) refers to app_diss.o(.text) for app_dis_init
    app_default_handlers.o(.text) refers to arch_sleep.o(.text) for arch_set_sleep_mode
    app_default_handlers.o(.text) refers to app.o(.text) for app_prf_enable
    app_default_handlers.o(.text) refers to user_peripheral.o(.text) for user_app_adv_start
    app_default_handlers.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_init
    app_default_handlers.o(.text) refers to otp_cs.o(.text) for otp_cs_get_adc_single_offset
    app_default_handlers.o(.text) refers to hash.o(.text) for hash
    app_default_handlers.o(.text) refers to app_easy_security.o(.text) for app_easy_security_send_pairing_rsp
    app_default_handlers.o(.text) refers to app_security.o(.text) for app_sec_gen_ltk
    app_default_handlers.o(.text) refers to app.o(retention_mem_area0) for app_env
    app_default_handlers.o(.text) refers to app_security.o(retention_mem_area0) for app_sec_env
    app_default_handlers.o(.text) refers to user_peripheral.o(.text) for user_app_adv_start
    app_default_handlers.o(.text) refers to app_easy_security.o(.text) for app_easy_security_tk_exch
    app_default_handlers.o(.text) refers to app_easy_security.o(.text) for app_easy_security_bdb_add_entry
    app_default_handlers.o(.text) refers to app_security.o(retention_mem_area0) for app_sec_env
    app_default_handlers.o(.constdata) refers to user_peripheral.o(.text) for user_app_adv_start
    app.o(.text) refers to strlen.o(.text) for strlen
    app.o(.text) refers to app_default_handlers.o(.text) for default_app_generate_unique_static_random_addr
    app.o(.text) refers to app.o(.constdata) for .constdata
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to user_custs_config.o(.constdata) for cust_prf_funcs
    app.o(.text) refers to user_custs1_impl.o(text) for device_config
    app.o(.text) refers to strlen.o(.text) for strlen
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to user_custs1_impl.o(text) for device_config
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app.o(.constdata) for .constdata
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app.o(.text) for app_easy_gap_directed_advertise_start_create_msg
    app.o(.text) refers to app.o(.text) for app_easy_gap_directed_advertise_start_create_msg
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app.o(.text) for app_easy_gap_non_connectable_advertise_start_create_msg
    app.o(.text) refers to app.o(.text) for app_easy_gap_non_connectable_advertise_start_create_msg
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer_cancel
    app.o(.text) refers to app.o(.text) for app_easy_gap_undirected_advertise_start
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app.o(.text) for app_easy_gap_advertise_stop_handler
    app.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer_cancel
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app.o(.text) for app_easy_gap_param_update_msg_create
    app.o(.text) refers to app.o(.text) for app_easy_gap_start_connection_to_msg_create
    app.o(.text) refers to app.o(.text) for app_easy_gap_start_connection_to_msg_create
    app.o(.text) refers to app.o(.text) for app_easy_gap_start_connection_to_msg_create
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app.o(.text) for app_easy_gap_dev_config_create_msg
    app.o(.constdata) refers to app_diss.o(.text) for app_diss_create_db
    app.o(.constdata) refers to app_task.o(retention_mem_area0) for app_state
    app.o(.constdata) refers to user_peripheral.o(.text) for user_app_connection
    app.o(.constdata) refers to app_default_handlers.o(.text) for default_app_on_set_dev_config_complete
    app_task.o(.text) refers to user_peripheral.o(.text) for user_catch_rest_hndl
    app_task.o(.text) refers to app.o(.text) for app_easy_gap_dev_configure
    app_task.o(.text) refers to app_default_handlers.o(.text) for default_app_on_set_dev_config_complete
    app_task.o(.text) refers to strlen.o(.text) for strlen
    app_task.o(.text) refers to app.o(retention_mem_area0) for app_env
    app_task.o(.text) refers to app_security.o(retention_mem_area0) for app_sec_env
    app_task.o(.text) refers to user_custs1_impl.o(text) for device_config
    app_task.o(.text) refers to app_task.o(.constdata) for .constdata
    app_task.o(.constdata) refers to user_peripheral.o(.text) for user_app_connection
    app_task.o(.constdata) refers to app_default_handlers.o(.text) for default_app_on_set_dev_config_complete
    app_task.o(.constdata) refers to app_task.o(.text) for gapm_device_ready_ind_handler
    app_security.o(.text) refers to app_utils.o(.text) for app_fill_random_byte_array
    app_security.o(.text) refers to app_security.o(retention_mem_area0) for retention_mem_area0
    app_security_task.o(.text) refers to app_default_handlers.o(.text) for default_app_on_pairing_request
    app_security_task.o(.text) refers to user_peripheral.o(.text) for user_app_on_tk_exch
    app_security_task.o(.text) refers to app.o(retention_mem_area0) for app_env
    app_security_task.o(.text) refers to app_security.o(retention_mem_area0) for app_sec_env
    app_security_task.o(.text) refers to app_security_task.o(.constdata) for .constdata
    app_security_task.o(.constdata) refers to user_peripheral.o(.text) for user_app_connection
    app_security_task.o(.constdata) refers to app_default_handlers.o(.text) for default_app_on_set_dev_config_complete
    app_security_task.o(.constdata) refers to app_security_task.o(.text) for gapc_bond_req_ind_handler
    app_diss_task.o(.text) refers to user_custs1_impl.o(.text) for Get_Software_Revision
    app_diss_task.o(.text) refers to printf1.o(i.__0sprintf$1) for __2sprintf
    app_diss_task.o(.text) refers to strlen.o(.text) for strlen
    app_diss_task.o(.text) refers to app_diss_task.o(.constdata) for .constdata
    app_diss_task.o(.constdata) refers to app_diss_task.o(.text) for diss_value_req_ind_handler
    app_entry_point.o(.constdata) refers to user_peripheral.o(.text) for user_app_connection
    app_entry_point.o(.constdata) refers to app_default_handlers.o(.text) for default_app_on_set_dev_config_complete
    app_entry_point.o(.constdata) refers to app_task.o(.text) for app_gap_process_handler
    app_entry_point.o(.constdata) refers to app_easy_timer.o(.text) for app_timer_api_process_handler
    app_entry_point.o(.constdata) refers to app_security_task.o(.text) for app_sec_process_handler
    app_entry_point.o(.constdata) refers to app_diss_task.o(.text) for app_diss_process_handler
    app_entry_point.o(.constdata) refers to app_customs_task.o(.text) for app_custs1_process_handler
    app_entry_point.o(.constdata) refers to app.o(.text) for app_easy_gap_dev_configure
    app_entry_point.o(.constdata) refers to app_entry_point.o(.constdata) for user_app_callbacks
    app_entry_point.o(.constdata) refers to user_peripheral.o(.text) for user_catch_rest_hndl
    app_entry_point.o(.constdata) refers to app_entry_point.o(.constdata) for app_process_handlers
    app_entry_point.o(.constdata) refers to app.o(retention_mem_area0) for app_env
    app_entry_point.o(.constdata) refers to app_security.o(retention_mem_area0) for app_sec_env
    app_entry_point.o(.constdata) refers to app.o(retention_mem_area0) for app_prf_srv_perm
    app_msg_utils.o(.text) refers to app_msg_utils.o(.text) for app_check_BLE_active
    app_msg_utils.o(.text) refers to arch_sleep.o(.text) for arch_ble_force_wakeup
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(.text) for app_easy_msg_find_idx
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(retention_mem_area0) for retention_mem_area0
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(retention_mem_area0) for retention_mem_area0
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(.text) for app_easy_msg_find_idx
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(retention_mem_area0) for retention_mem_area0
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(.text) for app_easy_msg_modify
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(.text) for app_easy_msg_set
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(retention_mem_area0) for retention_mem_area0
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(.text) for app_easy_msg_free_callback
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(retention_mem_area0) for retention_mem_area0
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(retention_mem_area0) for retention_mem_area0
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(.text) for app_easy_msg_find_idx
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(retention_mem_area0) for retention_mem_area0
    app_easy_security.o(.text) refers to app_security.o(.text) for app_sec_gen_csrk
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_init
    app_easy_security.o(.text) refers to app_easy_security.o(retention_mem_area0) for retention_mem_area0
    app_easy_security.o(.text) refers to app_easy_security.o(.constdata) for .constdata
    app_easy_security.o(.text) refers to app_security.o(retention_mem_area0) for app_sec_env
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for pairing_rsp_create_msg
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for tk_exch_create_msg
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for csrk_exch_create_msg
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for ltk_exch_create_msg
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for ltk_exch_create_msg
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for encrypt_cfm_create_msg
    app_easy_security.o(.text) refers to app_easy_security.o(retention_mem_area0) for retention_mem_area0
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for app_easy_security_request_get_active
    app_easy_security.o(.text) refers to app_easy_security.o(retention_mem_area0) for retention_mem_area0
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_get_stored_irks
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_get_number_of_stored_irks
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_get_number_of_stored_irks
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_get_stored_irks
    app_easy_security.o(.text) refers to app.o(retention_mem_area0) for app_env
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_get_size
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_get_device_info_from_slot
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_get_number_of_stored_irks
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for app_easy_security_bdb_get_device_info_from_slot
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for app_easy_security_ral_op
    app_easy_security.o(.constdata) refers to app_bond_db.o(.text) for default_app_bdb_init
    app_easy_security.o(.constdata) refers to app_bond_db.o(.text) for default_app_bdb_get_size
    app_easy_security.o(.constdata) refers to app_bond_db.o(.text) for default_app_bdb_get_number_of_stored_irks
    app_easy_security.o(.constdata) refers to app_bond_db.o(.text) for default_app_bdb_get_stored_irks
    app_easy_security.o(.constdata) refers to app_bond_db.o(.text) for default_app_bdb_get_device_info_from_slot
    app_easy_timer.o(.text) refers to app_msg_utils.o(.text) for app_check_BLE_active
    app_easy_timer.o(.text) refers to arch_sleep.o(.text) for arch_ble_force_wakeup
    app_easy_timer.o(.text) refers to app_easy_timer.o(retention_mem_area0) for retention_mem_area0
    app_easy_timer.o(.text) refers to app_easy_timer.o(.text) for timer_canceled_handler
    app_easy_timer.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer_cancel
    app_easy_timer.o(.text) refers to app_easy_timer.o(retention_mem_area0) for retention_mem_area0
    app_easy_timer.o(.text) refers to app_easy_timer.o(.text) for timer_canceled_handler
    app_customs.o(.text) refers to user_custs_config.o(.constdata) for cust_prf_funcs
    app_bond_db.o(.text) refers to user_custs1_impl.o(.text) for GetBondInfo
    app_bond_db.o(.text) refers to app_bond_db.o(retention_mem_area0) for retention_mem_area0
    app_bond_db.o(.text) refers to user_custs1_impl.o(.text) for notify_stm
    app_bond_db.o(.text) refers to app_bond_db.o(retention_mem_area0) for retention_mem_area0
    app_bond_db.o(.text) refers to app_bond_db.o(retention_mem_area0) for retention_mem_area0
    app_bond_db.o(.text) refers to app_bond_db.o(retention_mem_area0) for retention_mem_area0
    app_bond_db.o(.text) refers to app_bond_db.o(retention_mem_area0) for retention_mem_area0
    app_bond_db.o(.text) refers to uart.o(.text) for uart_send
    app_bond_db.o(.text) refers to app_bond_db.o(retention_mem_area0) for retention_mem_area0
    user_custs_config.o(.constdata) refers to user_custs1_def.o(.constdata) for custs1_att_db
    user_custs_config.o(.constdata) refers to app_customs.o(.text) for app_custs1_create_db
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for att_decl_svc
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for Ecg_Service
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for att_decl_char
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for ECG_SAMPLES_UUID
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for att_desc_cfg
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for PPG_SAMPLES_UUID
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for att_desc_user_desc
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.conststring) for .conststring
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for Vital_Service
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for VITAL_UUID
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for Alert_Service
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for ALERT_STATUS_UUID
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for User_Info_Service
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for USR_INFO_UUID_128
    user_periph_setup.o(.text) refers to gpio.o(.text) for GPIO_ConfigurePin
    user_periph_setup.o(.text) refers to gpio.o(.text) for GPIO_ConfigurePin
    user_periph_setup.o(.text) refers to syscntl.o(.text) for syscntl_dcdc_turn_on_in_boost
    user_periph_setup.o(.text) refers to patch.o(.text) for patch_func
    user_periph_setup.o(.text) refers to uart.o(.text) for uart_initialize
    user_periph_setup.o(.text) refers to uart.o(.text) for uart_read_byte
    user_periph_setup.o(.text) refers to user_periph_setup.o(.data) for .data
    user_periph_setup.o(.text) refers to user_periph_setup.o(.bss) for .bss
    user_periph_setup.o(.text) refers to uart.o(.text) for Close_Uart
    user_periph_setup.o(.text) refers to uart.o(.text) for uart_disable
    user_periph_setup.o(.text) refers to uart.o(.text) for Open_Uart
    user_periph_setup.o(.text) refers to user_periph_setup.o(.data) for .data
    user_periph_setup.o(.text) refers to uart.o(.text) for uart_initialize
    user_periph_setup.o(.text) refers to uart.o(.text) for uart_register_tx_cb
    user_periph_setup.o(.text) refers to uart.o(.text) for Open_Uart
    user_periph_setup.o(.text) refers to gpio.o(.text) for GPIO_ConfigurePin
    user_periph_setup.o(.text) refers to user_periph_setup.o(.data) for .data
    user_periph_setup.o(.text) refers to user_periph_setup.o(.text) for uart_send_cb
    user_periph_setup.o(.text) refers to uart.o(.text) for Close_Uart
    user_periph_setup.o(.text) refers to uart.o(.text) for uart_disable
    user_custs1_impl.o(.text) refers to comm_manager.o(.text) for Com_Send_Data
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(.bss) for .bss
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(text) for text
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(free_area) for free_area
    user_custs1_impl.o(.text) refers to app.o(retention_mem_area0) for app_env
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(.constdata) for .constdata
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(.bss) for .bss
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(.bss) for .bss
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(.bss) for .bss
    user_custs1_impl.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(.text) for ResetCommLock_cb
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(.bss) for .bss
    user_peripheral.o(.text) refers to user_custs1_impl.o(.text) for notify_stm
    user_peripheral.o(.text) refers to transportmanager.o(.text) for GetBufferXferContext
    user_peripheral.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer
    user_peripheral.o(.text) refers to user_peripheral.o(.bss) for .bss
    user_peripheral.o(.text) refers to user_peripheral.o(retention_mem_area0) for retention_mem_area0
    user_peripheral.o(.text) refers to user_peripheral.o(.data) for .data
    user_peripheral.o(.text) refers to app.o(.text) for app_easy_gap_param_update_start
    user_peripheral.o(.text) refers to gpio.o(.text) for GPIO_EnableIRQ
    user_peripheral.o(.text) refers to app_default_handlers.o(.text) for default_app_on_init
    user_peripheral.o(.text) refers to user_periph_setup.o(.text) for init_simple_ppg_uart
    user_peripheral.o(.text) refers to app_easy_security.o(.text) for app_easy_security_bdb_init
    user_peripheral.o(.text) refers to app_security.o(.text) for app_sec_gen_tk
    user_peripheral.o(.text) refers to serialinterface.o(.text) for uart_trigger
    user_peripheral.o(.text) refers to app_security.o(retention_mem_area0) for app_sec_env
    user_peripheral.o(.text) refers to app.o(retention_mem_area0) for app_env
    user_peripheral.o(.text) refers to user_custs1_impl.o(.bss) for Comm_Lock
    user_peripheral.o(.text) refers to user_custs1_impl.o(.text) for Send_To_Gatt_Client
    user_peripheral.o(.text) refers to user_custs1_impl.o(.text) for Send_To_Gatt_Client
    scheduler.o(.text) refers to user_custs1_impl.o(.text) for notify_stm
    scheduler.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer
    scheduler.o(.text) refers to scheduler.o(retention_mem_area0) for retention_mem_area0
    scheduler.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer
    scheduler.o(.text) refers to scheduler.o(retention_mem_area0) for retention_mem_area0
    scheduler.o(.text) refers to scheduler.o(.text) for SchedulerCallback
    scheduler.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer_modify
    scheduler.o(.text) refers to scheduler.o(retention_mem_area0) for retention_mem_area0
    scheduler.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer_cancel
    scheduler.o(.text) refers to scheduler.o(retention_mem_area0) for retention_mem_area0
    scheduler.o(.text) refers to scheduler.o(.bss) for .bss
    comm_task.o(.text) refers to primitivequeue.o(.text) for InitQueue
    comm_task.o(.text) refers to transportqueue.o(.text) for InitTriggerQueue
    comm_task.o(.text) refers to primitivemanager.o(.text) for resetPrimitiveManager
    comm_task.o(.text) refers to transportmanager.o(.text) for resetTransportManager
    comm_task.o(.text) refers to comm_manager.o(.text) for RunMainLoop
    comm_task.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer_modify
    comm_task.o(.text) refers to comm_task.o(.constdata) for .constdata
    comm_task.o(.text) refers to comm_task.o(retention_mem_area0) for retention_mem_area0
    comm_task.o(.text) refers to comm_task.o(.bss) for .bss
    comm_task.o(.constdata) refers to comm_task.o(.text) for comm_manager
    comm_task.o(.constdata) refers to comm_task.o(.constdata) for comm_default_state
    comm_task.o(.constdata) refers to comm_task.o(.constdata) for comm_handler
    comm_task.o(.constdata) refers to comm_task.o(retention_mem_area0) for comm_state
    serialinterface.o(.text) refers to uart.o(.text) for uart_send
    serialinterface.o(.text) refers to comm_task.o(.text) for triggerCommMngr
    serialinterface.o(.text) refers to gpio.o(.text) for GPIO_GetPinStatus
    serialinterface.o(.text) refers to comm_manager.o(.text) for COM_BreakEvent
    serialinterface.o(.text) refers to serialinterface.o(.data) for .data
    serialinterface.o(.text) refers to user_periph_setup.o(.text) for UART_OPEN_PORT_INIT
    serialinterface.o(.text) refers to user_periph_setup.o(.text) for UART_CLOSE_PORT_INIT
    serialinterface.o(.data) refers to serialinterface.o(.text) for uart_error_cb
    serialinterface.o(.data) refers to serialinterface.o(.text) for uart_send_cb
    upperlayerinterface.o(.text) refers to user_peripheral.o(.text) for updateMacIDPayload
    upperlayerinterface.o(.text) refers to transportmanager.o(.text) for GetBufferXferContext
    upperlayerinterface.o(.text) refers to upperlayerinterface.o(.bss) for .bss
    comm_manager.o(.text) refers to comm_task.o(.text) for triggerCommMngr
    comm_manager.o(.text) refers to primitivemanager.o(.text) for PrimitiveMain
    comm_manager.o(.text) refers to transportmanager.o(.text) for GetTransportTrigger
    comm_manager.o(.text) refers to comm_manager.o(.constdata) for .constdata
    comm_manager.o(.text) refers to comm_task.o(.text) for triggerCommMngr
    comm_manager.o(.text) refers to comm_task.o(.text) for triggerCommMngr
    comm_manager.o(.text) refers to comm_task.o(.text) for triggerCommMngr
    comm_manager.o(.text) refers to comm_task.o(.text) for triggerCommMngr
    comm_manager.o(.text) refers to comm_task.o(.text) for triggerCommMngr
    comm_manager.o(.text) refers to primitivemanager.o(.text) for resetPrimitiveManager
    comm_manager.o(.text) refers to transportmanager.o(.text) for resetTransportManager
    comm_manager.o(.text) refers to serialinterface.o(.text) for Close_Port
    primitivemanager.o(.text) refers to transportmanager.o(.text) for SetTransportTrigger
    primitivemanager.o(.text) refers to comm_task.o(.text) for stopCRTimer
    primitivemanager.o(.text) refers to primitivemanager.o(.bss) for .bss
    primitivemanager.o(.text) refers to primitivemanager.o(.data) for .data
    primitivemanager.o(.text) refers to primitivemanager.o(.constdata) for .constdata
    primitivemanager.o(.text) refers to primitivequeue.o(.text) for isCmdTxEmpty
    primitivemanager.o(.text) refers to comm_manager.o(.text) for WriteCmd
    primitivemanager.o(.text) refers to upperlayerinterface.o(.text) for BLE_ExecuteResponse
    primitivemanager.o(.text) refers to primitivemanager.o(.bss) for .bss
    primitivemanager.o(.text) refers to primitivemanager.o(.data) for .data
    primitivemanager.o(.text) refers to primitivemanager.o(.text) for resetPrimitiveManagerInd
    primitivemanager.o(.text) refers to primitivemanager.o(.data) for .data
    primitivemanager.o(.text) refers to primitivemanager.o(.bss) for .bss
    primitivemanager.o(.constdata) refers to primitivemanager.o(.text) for cmd_tx_event
    primitivequeue.o(.text) refers to primitivequeue.o(.bss) for .bss
    primitivequeue.o(.text) refers to primitivequeue.o(.data) for .data
    transportmanager.o(.text) refers to serialinterface.o(.text) for sendBreakCmd
    transportmanager.o(.text) refers to uart.o(.text) for rbuf_get
    transportmanager.o(.text) refers to primitivemanager.o(.text) for resetPrimitiveMaster
    transportmanager.o(.text) refers to comm_task.o(.text) for Comm_Sleep_Set
    transportmanager.o(.text) refers to transportmanager.o(.bss) for .bss
    transportmanager.o(.text) refers to transportmanager.o(.constdata) for .constdata
    transportmanager.o(.text) refers to transportmanager.o(.data) for .data
    transportmanager.o(.text) refers to upperlayerinterface.o(.text) for getMtuSize
    transportmanager.o(.text) refers to transportqueue.o(.text) for dequeueTrigger
    transportmanager.o(.text) refers to transportmanager.o(.bss) for .bss
    transportmanager.o(.constdata) refers to transportmanager.o(.text) for handle_close
    transportqueue.o(.text) refers to transportqueue.o(.bss) for .bss
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_da14531.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_da14531.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to arch_main.o(.text) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to arch_main.o(.text) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz

